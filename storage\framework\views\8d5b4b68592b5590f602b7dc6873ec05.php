<div>
    <!-- Lot List Modal -->
    <div class="modal fade" id="lotListModal" tabindex="-1" aria-labelledby="lotListModalLabel" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="lotListModalLabel">
                        <?php echo e($title ?? 'Endtime Lots'); ?>

                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="close"></button>
                </div>
                <div class="modal-body">
                    <!-- Filter Controls in a single row -->
                    <div class="row g-3 mb-4 align-items-end">
                        <!-- Line Filter -->
                        <div class="col-md-2">
                            <label for="lineFilter" class="form-label">Line</label>
                            <select class="form-select form-select-sm" id="lineFilter" wire:model="selectedLine" wire:change="updateLineFilter($event.target.value)">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableLines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $line): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($line); ?>"><?php echo e($line); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                        </div>

                        <!-- Work Type Filter -->
                        <div class="col-md-2">
                            <label for="workTypeFilter" class="form-label">WorkType</label>
                            <select class="form-select form-select-sm" id="workTypeFilter" wire:model="selectedWorkType" wire:change="updateWorkTypeFilter($event.target.value)">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableWorkTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $workType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($workType); ?>"><?php echo e($workType); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                        </div>

                        <!-- Lot Type Filter -->
                        <div class="col-md-2">
                            <label for="lotTypeFilter" class="form-label">LotType</label>
                            <select class="form-select form-select-sm" id="lotTypeFilter" wire:model="selectedLotType" wire:change="updateLotTypeFilter($event.target.value)">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableLotTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lotType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($lotType); ?>"><?php echo e($lotType); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                        </div>

                        <!-- Size Filter -->
                        <div class="col-md-2">
                            <label for="sizeFilter" class="form-label">Size</label>
                            <select class="form-select form-select-sm" id="sizeFilter" wire:model="selectedSize" wire:change="updateSizeFilter($event.target.value)">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableSizes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($size); ?>"><?php echo e($size); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                        </div>

                        <!-- Search Field -->
                        <div class="col-md-4">
                            <label for="searchField" class="form-label">Search</label>
                            <div class="input-group input-group-sm">
                                <input type="text" class="form-control" id="searchField"
                                       placeholder="Search..."
                                       wire:model.live.debounce.300ms="searchQuery">
                                <button class="btn btn-warning" wire:click="resetFilters">Reset</button>
                            </div>
                        </div>
                    </div>

                    <!-- Results Table -->
                    <div class="table-responsive">
                        <style>
                            .compact-header th {
                                white-space: nowrap;
                                padding: 0.5rem 0.5rem;
                                font-size: 0.875rem;
                                vertical-align: middle;
                            }
                            .compact-header .sort-icon {
                                margin-left: 2px;
                                font-size: 12px;
                                display: inline-block;
                            }
                            .compact-table td {
                                padding: 0.4rem 0.5rem;
                                font-size: 0.875rem;
                                vertical-align: middle;
                            }
                            .compact-table .badge {
                                font-size: 0.75rem;
                                padding: 0.25em 0.5em;
                            }
                        </style>
                        <table class="table table-bordered compact-table">
                            <thead>
                                <tr class="bg-light compact-header">
                                    <th wire:click="sortBy('lot_id')" style="cursor: pointer; width: 80px;">
                                        Lot No
                                        <!--[if BLOCK]><![endif]--><?php if($sortField === 'lot_id'): ?>
                                            <i class="ri-arrow-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?>-line sort-icon"></i>
                                        <?php else: ?>
                                            <i class="ri-arrow-up-down-line sort-icon" style="opacity: 0.3;"></i>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </th>
                                    <th wire:click="sortBy('model_id')" style="cursor: pointer; width: 140px;">
                                        Model
                                        <!--[if BLOCK]><![endif]--><?php if($sortField === 'model_id'): ?>
                                            <i class="ri-arrow-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?>-line sort-icon"></i>
                                        <?php else: ?>
                                            <i class="ri-arrow-up-down-line sort-icon" style="opacity: 0.3;"></i>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </th>
                                    <th wire:click="sortBy('lot_qty')" style="cursor: pointer; width: 80px;">
                                        Quantity
                                        <!--[if BLOCK]><![endif]--><?php if($sortField === 'lot_qty'): ?>
                                            <i class="ri-arrow-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?>-line sort-icon"></i>
                                        <?php else: ?>
                                            <i class="ri-arrow-up-down-line sort-icon" style="opacity: 0.3;"></i>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </th>
                                    <th wire:click="sortBy('area')" style="cursor: pointer; width: 50px;">
                                        Area
                                        <!--[if BLOCK]><![endif]--><?php if($sortField === 'area'): ?>
                                            <i class="ri-arrow-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?>-line sort-icon"></i>
                                        <?php else: ?>
                                            <i class="ri-arrow-up-down-line sort-icon" style="opacity: 0.3;"></i>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </th>
                                    <th wire:click="sortBy('mc_no')" style="cursor: pointer; width: 60px;">
                                        MC No
                                        <!--[if BLOCK]><![endif]--><?php if($sortField === 'mc_no'): ?>
                                            <i class="ri-arrow-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?>-line sort-icon"></i>
                                        <?php else: ?>
                                            <i class="ri-arrow-up-down-line sort-icon" style="opacity: 0.3;"></i>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </th>
                                    <th wire:click="sortBy('chip_size')" style="cursor: pointer; width: 50px;">
                                        Size
                                        <!--[if BLOCK]><![endif]--><?php if($sortField === 'chip_size'): ?>
                                            <i class="ri-arrow-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?>-line sort-icon"></i>
                                        <?php else: ?>
                                            <i class="ri-arrow-up-down-line sort-icon" style="opacity: 0.3;"></i>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </th>
                                    <th wire:click="sortBy('work_type')" style="cursor: pointer; width: 90px;">
                                        WorkType
                                        <!--[if BLOCK]><![endif]--><?php if($sortField === 'work_type'): ?>
                                            <i class="ri-arrow-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?>-line sort-icon"></i>
                                        <?php else: ?>
                                            <i class="ri-arrow-up-down-line sort-icon" style="opacity: 0.3;"></i>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </th>
                                    <th wire:click="sortBy('lot_type')" style="cursor: pointer; width: 70px;">
                                        LotType
                                        <!--[if BLOCK]><![endif]--><?php if($sortField === 'lot_type'): ?>
                                            <i class="ri-arrow-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?>-line sort-icon"></i>
                                        <?php else: ?>
                                            <i class="ri-arrow-up-down-line sort-icon" style="opacity: 0.3;"></i>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </th>
                                    <th wire:click="sortBy('status')" style="cursor: pointer; width: 80px;">
                                        Status
                                        <!--[if BLOCK]><![endif]--><?php if($sortField === 'status'): ?>
                                            <i class="ri-arrow-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?>-line sort-icon"></i>
                                        <?php else: ?>
                                            <i class="ri-arrow-up-down-line sort-icon" style="opacity: 0.3;"></i>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $paginatedLots['data']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lot): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($lot['lot_id']); ?></td>
                                    <td><?php echo e($lot['model_id']); ?></td>
                                    <td><?php echo e(number_format($lot['lot_qty'])); ?></td>
                                    <td><?php echo e($lot['area']); ?></td>
                                    <td><?php echo e($lot['mc_no']); ?></td>
                                    <td><?php echo e($lot['chip_size']); ?></td>
                                    <td><?php echo e($lot['work_type']); ?></td>
                                    <td><?php echo e($lot['lot_type']); ?></td>
                                    <td class="text-center">
                                        <span class="badge <?php echo e(strtoupper($lot['status']) == 'SUBMITTED' ? 'bg-success' : 'bg-warning'); ?>">
                                            <?php echo e($lot['status']); ?>

                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="9" class="text-center">No lots found</td>
                                </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <!--[if BLOCK]><![endif]--><?php if($paginatedLots['total'] > 0): ?>
                                <nav>
                                    <ul class="pagination">
                                        <!-- Previous Page Link -->
                                        <li class="page-item <?php echo e($paginatedLots['current_page'] == 1 ? 'disabled' : ''); ?>">
                                            <a class="page-link" href="#" wire:click.prevent="previousPage">&laquo;</a>
                                        </li>

                                        <!-- Page Number Links -->
                                        <?php
                                            $startPage = max(1, $paginatedLots['current_page'] - 2);
                                            $endPage = min($paginatedLots['last_page'], $startPage + 4);

                                            if ($endPage - $startPage < 4 && $startPage > 1) {
                                                $startPage = max(1, $endPage - 4);
                                            }
                                        ?>

                                        <!--[if BLOCK]><![endif]--><?php if($startPage > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="#" wire:click.prevent="gotoPage(1)">1</a>
                                            </li>
                                            <!--[if BLOCK]><![endif]--><?php if($startPage > 2): ?>
                                                <li class="page-item disabled">
                                                    <span class="page-link">...</span>
                                                </li>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                        <!--[if BLOCK]><![endif]--><?php for($i = $startPage; $i <= $endPage; $i++): ?>
                                            <li class="page-item <?php echo e($paginatedLots['current_page'] == $i ? 'active' : ''); ?>">
                                                <a class="page-link" href="#" wire:click.prevent="gotoPage(<?php echo e($i); ?>)"><?php echo e($i); ?></a>
                                            </li>
                                        <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->

                                        <!--[if BLOCK]><![endif]--><?php if($endPage < $paginatedLots['last_page']): ?>
                                            <!--[if BLOCK]><![endif]--><?php if($endPage < $paginatedLots['last_page'] - 1): ?>
                                                <li class="page-item disabled">
                                                    <span class="page-link">...</span>
                                                </li>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            <li class="page-item">
                                                <a class="page-link" href="#" wire:click.prevent="gotoPage(<?php echo e($paginatedLots['last_page']); ?>)"><?php echo e($paginatedLots['last_page']); ?></a>
                                            </li>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                        <!-- Next Page Link -->
                                        <li class="page-item <?php echo e($paginatedLots['current_page'] == $paginatedLots['last_page'] ? 'disabled' : ''); ?>">
                                            <a class="page-link" href="#" wire:click.prevent="nextPage">&raquo;</a>
                                        </li>
                                    </ul>
                                </nav>
                                <div class="text-muted small">
                                    Showing <?php echo e(($paginatedLots['current_page'] - 1) * $paginatedLots['per_page'] + 1); ?>

                                    to <?php echo e(min($paginatedLots['current_page'] * $paginatedLots['per_page'], $paginatedLots['total'])); ?>

                                    of <?php echo e($paginatedLots['total']); ?> results
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        <div>
                            <button type="button" class="btn btn-danger" data-bs-dismiss="modal" wire:click="close">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript to open the modal -->
    <script>
        document.addEventListener('livewire:initialized', function () {
            Livewire.on('showLotList', () => {
                const modal = new bootstrap.Modal(document.getElementById('lotListModal'));
                modal.show();
            });
        });
    </script>
</div>
<?php /**PATH C:\inetpub\wwwroot\centralize-v1\resources\views/livewire/endtime-dashboard/lot-list-modal.blade.php ENDPATH**/ ?>