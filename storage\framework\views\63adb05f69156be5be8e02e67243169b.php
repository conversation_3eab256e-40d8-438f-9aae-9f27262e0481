<div>
    <div class="card custom-card">
        <div class="card-header">
            <div class="d-flex align-items-center">
                <div class="card-title">Submitted per Cutoff</div>
                <div wire:loading class="spinner-border spinner-border-sm text-primary ms-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-cutoff mb-0 text-nowrap" style="font-size: 0.9rem;">
                    <thead>
                        <tr>
                            <th class="text-center bg-primary-light text-primary">Line</th>
                            <th class="text-center bg-primary-light text-primary">Target</th>
                            <?php $index = 0; ?>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cutoffNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cutoffTime => $cutoffDisplay): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php $isEven = $index % 2 == 0; $index++; ?>
                                <th class="text-center cutoff-border-start bg-primary-light text-primary<?php echo e($isEven ? 'cutoff-section' : ''); ?>"><?php echo e($cutoffDisplay); ?> Qty</th>
                                <th class="text-center cutoff-border-end bg-primary-light text-primary<?php echo e($isEven ? 'cutoff-section' : ''); ?>"><?php echo e($cutoffDisplay); ?> %</th>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                    </thead>
                    <tbody>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $lineData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $line): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="hover-row">
                            <td class="text-center"><?php echo e($line['line']); ?></td>
                            <td class="text-center"><?php echo e(number_format($line['target'] / 1000000, 2)); ?>M</td>

                            <?php $index = 0; ?>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cutoffNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cutoffTime => $cutoffDisplay): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php $isEven = $index % 2 == 0; $index++; ?>
                                <td class="text-center cutoff-border-start <?php echo e($isEven ? 'cutoff-section' : ''); ?>"><?php echo e($line['cutoffs'][$cutoffTime]['qty'] > 0 ? number_format($line['cutoffs'][$cutoffTime]['qty'] / 1000000, 2) . 'M' : '0M'); ?></td>
                                <td class="text-center cutoff-border-end <?php echo e($isEven ? 'cutoff-section' : ''); ?> <?php echo e($line['cutoffs'][$cutoffTime]['percentage'] >= 100 ? 'text-success' : ($line['cutoffs'][$cutoffTime]['percentage'] >= 90 ? 'text-secondary' : 'text-danger')); ?>">
                                    <?php echo e($line['cutoffs'][$cutoffTime]['percentage'] > 0 ? $line['cutoffs'][$cutoffTime]['percentage'] : '0.0'); ?>%
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Total Row -->
                        <tr class="fw-bold bg-light">
                            <td class="text-center">Total</td>
                            <td class="text-center"><?php echo e(number_format($totalTarget / 1000000, 2)); ?>M</td>

                            <?php $index = 0; ?>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cutoffNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cutoffTime => $cutoffDisplay): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php $isEven = $index % 2 == 0; $index++; ?>
                                <td class="text-center cutoff-border-start <?php echo e($isEven ? 'cutoff-section' : ''); ?>"><?php echo e($totalCutoffs[$cutoffTime]['qty'] > 0 ? number_format($totalCutoffs[$cutoffTime]['qty'] / 1000000, 2) . 'M' : '0M'); ?></td>
                                <td class="text-center cutoff-border-end <?php echo e($isEven ? 'cutoff-section' : ''); ?> <?php echo e($totalCutoffs[$cutoffTime]['percentage'] >= 100 ? 'text-success' : ($totalCutoffs[$cutoffTime]['percentage'] >= 90 ? 'text-secondary' : 'text-danger')); ?>">
                                    <?php echo e($totalCutoffs[$cutoffTime]['percentage'] > 0 ? $totalCutoffs[$cutoffTime]['percentage'] : '0.0'); ?>%
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <style>
        .hover-row:hover {
            background-color: var(--bs-secondary-bg) !important;
        }
        .cutoff-border-start {
            border-left: 2px solid #495057 !important;
        }
        .cutoff-border-end {
            border-right: 2px solid #495057 !important;
        }
        .table-cutoff {
            border-collapse: separate;
            border-spacing: 0;
        }
        .table-cutoff th, .table-cutoff td {
            border: 1px solid #495057;
        }

        /* Add background color to separate cutoff sections */
        .cutoff-section {
            background-color: rgba(52, 58, 64, 0.3);
        }
    </style>
</div>
<?php /**PATH C:\inetpub\wwwroot\centralize-v1\resources\views/livewire/endtime-dashboard/submitted-per-cutoff-table.blade.php ENDPATH**/ ?>