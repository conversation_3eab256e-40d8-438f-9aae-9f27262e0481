<div>
    <div class="card custom-card">
        <div class="card-header d-flex align-items-center justify-content-between">
            <div class="card-title">Achievement per Line</div>
            <div wire:loading class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-cutoff mb-0 text-nowrap">
                    <thead>
                        <tr>
                            <th class="fw-bold bg-primary-light text-primary">Line</th>
                            <th class="fw-bold bg-primary-light text-primary">Total</th>
                            <th class="fw-bold bg-primary-light text-primary">A</th>
                            <th class="fw-bold bg-primary-light text-primary">B</th>
                            <th class="fw-bold bg-primary-light text-primary">C</th>
                            <th class="fw-bold bg-primary-light text-primary">D</th>
                            <th class="fw-bold bg-primary-light text-primary">E</th>
                            <th class="fw-bold bg-primary-light text-primary">F</th>
                            <th class="fw-bold bg-primary-light text-primary">G</th>
                            <th class="fw-bold bg-primary-light text-primary">H</th>
                            <th class="fw-bold bg-primary-light text-primary">I</th>
                            <th class="fw-bold bg-primary-light text-primary">J</th>
                            <th class="fw-bold bg-primary-light text-primary">VMI</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="hover-row text-info">
                            <td class="fw-semibold">Target</td>
                            <td><?php echo e(number_format(array_sum($targetData) / 1000000, 2)); ?>M</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $targetData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $target): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td><?php echo e(number_format($target / 1000000, 2)); ?>M</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                        <tr class="hover-row text-primary">
                            <td class="fw-semibold">Endtime</td>
                            <td><?php echo e(number_format(array_sum($endtimeData) / 1000000, 2)); ?>M</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $endtimeData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $endtime): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td><?php echo e(number_format($endtime / 1000000, 2)); ?>M</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                        <tr class="hover-row text-success">
                            <td class="fw-semibold">Submitted</td>
                            <td><?php echo e(number_format(array_sum($submittedData) / 1000000, 2)); ?>M</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $submittedData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $submitted): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td><?php echo e(number_format($submitted / 1000000, 2)); ?>M</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                        <tr class="hover-row text-danger">
                            <td class="fw-semibold">Remaining</td>
                            <td><?php echo e(number_format(array_sum($remainingData) / 1000000, 2)); ?>M</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $remainingData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $remaining): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td><?php echo e(number_format($remaining / 1000000, 2)); ?>M</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                        <tr class="hover-row">
                            <td class="fw-semibold">Endtime %</td>
                            <?php
                                $totalTargetSum = array_sum($targetData) ?: 1; // Avoid division by zero
                                $totalEndtimePercentage = round((array_sum($endtimeData) / $totalTargetSum) * 100, 1);
                            ?>
                            <td class="<?php echo e($totalEndtimePercentage >= 100 ? 'text-success' : 'text-danger'); ?>"><?php echo e($totalEndtimePercentage); ?>%</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $endtimePercentages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $percentage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="<?php echo e($percentage >= 100 ? 'text-success' : 'text-danger'); ?>"><?php echo e($percentage); ?>%</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                        <tr class="hover-row">
                            <td class="fw-semibold">Submitted %</td>
                            <?php
                                $totalSubmittedPercentage = round((array_sum($submittedData) / $totalTargetSum) * 100, 1);
                            ?>
                            <td class="<?php echo e($totalSubmittedPercentage >= 100 ? 'text-success' : 'text-danger'); ?>"><?php echo e($totalSubmittedPercentage); ?>%</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $submittedPercentages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $percentage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="<?php echo e($percentage >= 100 ? 'text-success' : 'text-danger'); ?>"><?php echo e($percentage); ?>%</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <style>
        .bg-primary-light {
            background-color: rgba(var(--primary-rgb), 0.15) !important;
        }

        .hover-row:hover {
            font-weight: bold !important;
        }
        .hover-row:hover td {
            font-weight: bold !important;
        }

        .cutoff-border-start {
            border-left: 1px solid #6c757d !important;
        }
        .cutoff-border-end {
            border-right: 1px solid #6c757d !important;
        }
        .table-cutoff {
            border-collapse: separate;
            border-spacing: 0;
        }
        .table-cutoff th, .table-cutoff td {
            border: 1px solid #495057;
        }
    </style>
</div>
<?php /**PATH C:\inetpub\wwwroot\centralize-v1\resources\views/livewire/endtime-dashboard/line-achievement-table.blade.php ENDPATH**/ ?>