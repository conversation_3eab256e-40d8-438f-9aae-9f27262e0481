<div>
    <div class="card custom-card">
        <div class="card-header d-flex align-items-center justify-content-between">
            <div class="card-title">Achievement per Size</div>
            <div wire:loading class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-cutoff mb-0 text-nowrap">
                    <thead>
                        <tr>
                            <th class="fw-semibold bg-primary-light text-primary">Size</th>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $sizeNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <th class="fw-semibold bg-primary-light text-primary <?php echo e($index > 0 ? 'cutoff-border-start' : ''); ?>"><?php echo e($size); ?></th>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="hover-row text-info">
                            <td class="fw-semibold">Target</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $targetData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $target): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="text-info <?php echo e($index > 0 ? 'cutoff-border-start' : ''); ?>"><?php echo e(number_format($target / 1000000, 2)); ?>M</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                        <tr class="hover-row text-primary">
                            <td class="fw-semibold">Endtime</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $endtimeData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $endtime): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="text-primary <?php echo e($index > 0 ? 'cutoff-border-start' : ''); ?>"><?php echo e(number_format($endtime / 1000000, 2)); ?>M</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                        <tr class="hover-row text-success">
                            <td class="fw-semibold">Submitted</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $submittedData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $submitted): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="text-success <?php echo e($index > 0 ? 'cutoff-border-start' : ''); ?>"><?php echo e(number_format($submitted / 1000000, 2)); ?>M</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                        <tr class="hover-row text-danger">
                            <td class="fw-semibold">Remaining</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $remainingData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $remaining): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="text-danger <?php echo e($index > 0 ? 'cutoff-border-start' : ''); ?>"><?php echo e(number_format($remaining / 1000000, 2)); ?>M</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                        <tr class="hover-row">
                            <td class="fw-semibold">Endtime %</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $endtimePercentages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $percentage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="<?php echo e($index > 0 ? 'cutoff-border-start' : ''); ?> <?php echo e($percentage >= 100 ? 'text-success' : 'text-danger'); ?>"><?php echo e($percentage); ?>%</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                        <tr class="hover-row">
                            <td class="fw-semibold">Submitted %</td>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $submittedPercentages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $percentage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="<?php echo e($index > 0 ? 'cutoff-border-start' : ''); ?> <?php echo e($percentage >= 100 ? 'text-success' : 'text-danger'); ?>"><?php echo e($percentage); ?>%</td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <style>
        .hover-row:hover {
            background-color: var(--bs-secondary-bg) !important;
            font-weight: bold !important;
        }
        .hover-row:hover td {
            font-weight: bold !important;
        }

        /* Keep percentage colors unchanged */
        tr:nth-child(5).hover-row:hover td.text-success,
        tr:nth-child(6).hover-row:hover td.text-success {
            color: rgb(var(--success-rgb)) !important;
        }
        tr:nth-child(5).hover-row:hover td.text-danger,
        tr:nth-child(6).hover-row:hover td.text-danger {
            color: rgb(var(--danger-rgb)) !important;
        }

        /* Grid separation styles */
        .cutoff-border-start {
            border-left: 1px solid #6c757d !important;
        }
        .cutoff-border-end {
            border-right: 1px solid #6c757d !important;
        }
        .table-cutoff {
            border-collapse: separate;
            border-spacing: 0;
        }
        .table-cutoff th, .table-cutoff td {
            border: 1px solid #495057;
        }
    </style>
</div>
<?php /**PATH C:\inetpub\wwwroot\centralize-v1\resources\views/livewire/endtime-dashboard/size-achievement-table.blade.php ENDPATH**/ ?>