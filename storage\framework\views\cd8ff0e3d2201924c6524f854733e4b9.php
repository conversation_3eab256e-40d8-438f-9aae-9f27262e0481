
<?php $__env->startSection('title', 'Endtime & Submitted'); ?>

<?php $__env->startSection('content'); ?>

<?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('endtime-dashboard');

$__html = app('livewire')->mount($__name, $__params, 'lw-3873454316-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- APEX CHARTS JS -->
<script src="<?php echo e(asset('vendor/apexcharts/apexcharts.min.js')); ?>"></script>

<!-- LIVEWIRE ENDTIME JS -->
<script src="<?php echo e(asset('js/livewire-endtime.js')); ?>"></script>

<!-- ENDTIME BUTTONS JS -->
<script src="<?php echo e(asset('js/endtime-buttons.js')); ?>"></script>

<!-- ENDTIME ENTRY JS -->
<script src="<?php echo e(asset('js/endtime-entry.js')); ?>"></script>

<!-- MODAL FIX JS -->
<script src="<?php echo e(asset('js/modal-fix.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.pages.endtime', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\inetpub\wwwroot\centralize-v1\resources\views/pages/endtime.blade.php ENDPATH**/ ?>